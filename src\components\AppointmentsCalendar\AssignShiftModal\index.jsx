// styled components
import { Container } from "./style";
import { Input } from "@ui/Field";
import DateInput from "@components/MaskedInputs/Date";

// components
import Btn from "@ui/Btn";
import ModalWindow from "@components/ModalWindow";
import LabeledFormInput from "@ui/LabeledFormInput";
import CustomSelect from "@ui/Select";
import { breakpoints } from "@styles/vars";
import styled from "styled-components";
import { Box, Checkbox, Divider, FormControlLabel, FormLabel, Typography } from "@mui/material";
import { CheckBox as CheckboxIcon, CheckBoxOutlineBlank } from "@mui/icons-material";
import TextArea from "@ui/TextArea/TextArea";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState, useRef } from "react";
import moment from "moment";
import { doc, setDoc, Timestamp } from "firebase/firestore";
import { getFunctions, httpsCallable } from "firebase/functions";
import { useDispatch, useSelector } from "react-redux";
import { nanoid } from "nanoid";
import { db } from "@config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import { addNewAppointmentAction, addMultipleAppointmentsAction } from "@store/slices/appointments";
// Removed calculateNextVisit import - now handled by cloud functions
import { useSnackbar } from "notistack";
import { checkAppointmentConflict, formatConflictErrorMessage, checkForExactDuplicate, checkCaregiverAppointmentConflict, formatCaregiverConflictErrorMessage } from "@utils/appointmentValidation";
import { collection, getDocs, query, where, getDoc } from "firebase/firestore";

const InnerContainer = styled.div`
  display: grid;
  grid-gap: 16px;
  margin: 24px 0;

  ${breakpoints.landscapeS} {
    grid-template-columns: 1fr 1fr;
  }
`;

const SHIFT_LENGTH = [
  { value: 1, label: "1 hour" },
  { value: 2, label: "2 hours" },
  { value: 3, label: "3 hours" },
  { value: 4, label: "4 hours" },
  { value: 5, label: "5 hours" },
  { value: 6, label: "6 hours" },
  { value: 7, label: "7 hours" },
  { value: 8, label: "8 hours" },
  { value: 9, label: "9 hours" },
  { value: 10, label: "10 hours" },
  { value: 11, label: "11 hours" },
  { value: 12, label: "12 hours" },
];

const TIME_OPTIONS = [
  { value: "06:00", label: "6:00 AM" },
  { value: "07:00", label: "7:00 AM" },
  { value: "08:00", label: "8:00 AM" },
  { value: "09:00", label: "9:00 AM" },
  { value: "10:00", label: "10:00 AM" },
  { value: "11:00", label: "11:00 AM" },
  { value: "12:00", label: "12:00 PM" },
  { value: "13:00", label: "1:00 PM" },
  { value: "14:00", label: "2:00 PM" },
  { value: "15:00", label: "3:00 PM" },
  { value: "16:00", label: "4:00 PM" },
  { value: "17:00", label: "5:00 PM" },
  { value: "18:00", label: "6:00 PM" },
  { value: "19:00", label: "7:00 PM" },
  { value: "20:00", label: "8:00 PM" },
  { value: "21:00", label: "9:00 PM" },
  { value: "22:00", label: "10:00 PM" },
];

const RECURRENCE = [
  { value: "daily", label: "Daily" },
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
  { value: "60-days", label: "60 Days" },
];

const SERVICE_OPTIONS = [
  { label: "Auxiliary Nurse", value: "auxiliary_nurse" },
  { label: "Home Health Aide", value: "home_health_aide" },
  { label: "Personal Support Worker", value: "personal_support_worker" },
  { label: "Palliative Caregiver", value: "palliative_caregiver" },
  { label: "Dementia Caregiver", value: "dementia_caregiver" },
];

const shiftSchema = z
  .object({
    shiftLength: z.coerce.number().optional(),
    overrideShift: z.boolean().default(false),
    caregiver: z.string().min(1, "Caregiver is required"),
    scheduleWithoutTime: z.boolean(),
    recurrence: z.string().optional(),
    startDate: z.string().min(1, "Start date is required"),
    endDate: z.string().min(1, "End date is required"),
    serviceType: z.string().min(1, "Service Type is required"),
    startTime: z.string().optional(),
    endTime: z.string().optional(),
    isFromCalendar: z.boolean().default(false),
    comments: z.string().optional(),
    submission_type: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    // Condition: shiftLength is required if overrideShift is false
    if (!data.overrideShift) {
      if (data.shiftLength === undefined || isNaN(data.shiftLength)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Shift length is required",
          path: ["shiftLength"],
        });
      }
    }

    // Condition: startTime and endTime are required and valid if scheduleWithoutTime is false
    if (!data.scheduleWithoutTime) {
      if (!data.startTime || data.startTime.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Start time is required",
          path: ["startTime"],
        });
      }

      if (!data.endTime || data.endTime.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "End time is required",
          path: ["endTime"],
        });
      }

      if (data.startTime && data.endTime) {
        const startDateTime = moment(data.startTime, "HH:mm");
        const endDateTime = moment(data.endTime, "HH:mm");

        if (!endDateTime.isAfter(startDateTime)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "End time must be after start time",
            path: ["endTime"],
          });
        }
      }
    }

    // Condition: Date must be within allowed range
    if (data.startDate) {
      const selectedDate = moment(data.startDate);
      const threeMonthsFromNow = moment().add(2, "months").endOf("month");

      if (selectedDate.isAfter(threeMonthsFromNow)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select a valid date",
          path: ["startDate"],
        });
      }
    }
  });

const AssignShiftModal = ({ name, open, handler, date, mode, defaultValues, commonNurse }) => {
  const isFromCalendar = mode === "caregiver_calendar";

  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  const { caregivers, clients } = useSelector((state) => state.users);
  const { user } = useSelector((state) => state.auth);

  // State to prevent duplicate submissions
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);
  const submissionInProgress = useRef(false);
  const lastSubmissionTime = useRef(0);
  const SUBMISSION_DEBOUNCE_MS = 2000; // 2 seconds debounce

  // Get the nurse ID - use commonNurse prop, or current user if they're a nurse, or find from client
  const getNurseId = () => {
    if (user?.role === "NURSE") return user.id;
    if (defaultValues?.client) {
      const client = clients?.find((c) => c.id === defaultValues.client);
      return client?.assignedNurse;
    }
    return null;
  };

  const {
    handleSubmit,
    register,
    setValue,
    setError,
    watch,
    reset,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: {
      overrideShift: false,
      scheduleWithoutTime: false,
      startDate: "",
      endDate: "",
      startTime: "",
      endTime: "",
      recurrence: "",
      serviceType: "",
      caregiver: "",
      comments: "",
      submission_type: "",
    },
    resolver: zodResolver(shiftSchema),
  });
  const caregivers_options = caregivers
    ?.filter((item) => item?.type === watch("serviceType"))
    ?.map((item) => ({ label: item?.name, value: item?.id }));

  function onCloseModal() {
    handler(false);
    reset({
      overrideShift: false,
      scheduleWithoutTime: false,
      startDate: "",
      endDate: "",
      startTime: "",
      endTime: "",
      recurrence: "",
      serviceType: "",
      caregiver: "",
      comments: "",
      submission_type: "",
    });
    // Reset submission state when modal closes
    setIsFormSubmitting(false);
    submissionInProgress.current = false;
    lastSubmissionTime.current = 0;
  }

  function onSelectShiftLength(val) {
    setValue("shiftLength", Number(val));
    setError("shiftLength", { message: undefined });
    if (watch("startTime") && !watch("overrideShift")) {
      const end_time = moment(watch("startTime"), "HH:mm").add(val, "hours").format("HH:mm");
      setValue("endTime", end_time);
    }
  }

  function onSelectStartTime(selectedOption) {
    const timeValue = selectedOption?.value;
    setValue("startTime", timeValue);
    setError("startTime", { message: undefined });
    if (watch("shiftLength") && !watch("overrideShift") && timeValue) {
      const end_time = moment(timeValue, "HH:mm").add(watch("shiftLength"), "hours");
      setValue("endTime", end_time.format("HH:mm"));
      setError("endTime", { message: undefined });
    }
  }

  function onSelectEndTime(selectedOption) {
    const timeValue = selectedOption?.value;
    setValue("endTime", timeValue);
    setError("endTime", { message: undefined });
  }

  async function submitForm(formValues) {
    const currentTime = Date.now();

    // Prevent duplicate submissions with debounce
    if (isFormSubmitting || submissionInProgress.current) {
      console.log("Submission already in progress, ignoring duplicate request");
      return;
    }

    // Debounce rapid successive submissions
    if (currentTime - lastSubmissionTime.current < SUBMISSION_DEBOUNCE_MS) {
      console.log("Submission too soon after last attempt, ignoring");
      return;
    }

    console.log(formValues);

    // Set submission state
    setIsFormSubmitting(true);
    submissionInProgress.current = true;
    lastSubmissionTime.current = currentTime;

    try {
      // * Create caregiver appointment
      if (formValues.caregiver) {
        // Check for patient appointment conflicts only if scheduling with specific time
        if (!formValues.scheduleWithoutTime && defaultValues?.client) {
          try {
            const conflictCheck = await checkAppointmentConflict(
              defaultValues.client,
              formValues.startDate,
              formValues.startTime,
              formValues.endTime
            );
            if (conflictCheck.hasConflict) {
              const errorMessage = formatConflictErrorMessage(conflictCheck.conflictingAppointments);
              setError("startTime", { type: "manual", message: errorMessage });
              enqueueSnackbar("Patient appointment conflict detected. Please choose a different time.", { variant: "error" });
              return;
            }
          } catch (error) {
            enqueueSnackbar(`Error validating patient appointment time: ${error.message}`, { variant: "error" });
            return;
          }
        }

        // Check for caregiver appointment conflicts only if scheduling with specific time
        if (!formValues.scheduleWithoutTime) {
          try {
            const caregiverConflictCheck = await checkCaregiverAppointmentConflict(
              formValues.caregiver,
              formValues.startDate,
              formValues.startTime,
              formValues.endTime
            );
            if (caregiverConflictCheck.hasConflict) {
              const errorMessage = formatCaregiverConflictErrorMessage(caregiverConflictCheck.conflictingAppointments);
              setError("caregiver", { type: "manual", message: errorMessage });
              enqueueSnackbar("Caregiver scheduling conflict detected. Please choose a different caregiver or time.", { variant: "error" });
              return;
            }
          } catch (error) {
            enqueueSnackbar(`Error validating caregiver availability: ${error.message}`, { variant: "error" });
            return;
          }
        }

        // Check for exact duplicate appointments
        try {
          const isDuplicate = await checkForExactDuplicate(
            defaultValues?.client,
            formValues.caregiver,
            formValues.startDate,
            formValues.startTime,
            formValues.endTime
          );
          if (isDuplicate) {
            enqueueSnackbar("An identical appointment already exists for this patient, caregiver, date and time.", { variant: "error" });
            return;
          }
        } catch (error) {
          console.error("Error checking for duplicate appointment:", error);
          // Continue with creation if duplicate check fails
        }

        const appointmentData = {
          nurse: getNurseId(),
          caregiver: formValues.caregiver,
          client: defaultValues?.client,
          serviceType: formValues.serviceType,
          status: "SCHEDULED",
          comments: formValues.comments,
        };

        // Handle recurrence if specified
        if (formValues.recurrence) {
          appointmentData.recurrence = {
            frequence: formValues.recurrence,
            interval: 1, // Default to every occurrence
          };

          // Add specific recurrence rules based on frequency
          if (formValues.recurrence === "weekly") {
            // Default to the same day of week as start date
            const startDay = moment(formValues.startDate).format("ddd").toLowerCase();
            appointmentData.recurrence.daysOfWeek = [startDay];
          } else if (formValues.recurrence === "monthly") {
            // Default to the same date of month as start date
            const startDate = moment(formValues.startDate).date().toString();
            appointmentData.recurrence.monthlyDates = [startDate];
          } else if (formValues.recurrence === "60-days") {
            // For 60-days, we don't need additional rules
            appointmentData.recurrence.interval = 60;
            appointmentData.recurrence.frequence = "60-days";
          }
        }
        // else {
        //   appointmentData.recurrence = null; // No recurrence
        //   appointmentData.isRecurringInstance = false; // Not a recurring instance
        //   appointmentData.refID= 
        // }

        if (formValues.scheduleWithoutTime) {
          appointmentData.startDateTime = `${formValues.startDate} 00:00`;
          appointmentData.endDateTime = `${formValues.endDate} 23:59`;
          appointmentData.isScheduleWithTime = false;
        } else {
          appointmentData.isScheduleWithTime = true;
          appointmentData.startDateTime = `${formValues.startDate} ${formValues.startTime}`;
          appointmentData.endDateTime = `${formValues.endDate} ${formValues.endTime}`;
        }

        console.log("Appointment data:", appointmentData);

        // Call cloud function to generate appointments
        const functions = getFunctions();
        const generateRecurringAppointments = httpsCallable(functions, 'generateRecurringAppointments');

        try {
          const result = await generateRecurringAppointments({ appointmentData });
          const { success, appointmentIds, count, recurrenceType, refID } = result.data;

          if (success) {
            // For recurring appointments, fetch all created appointments from Firebase and add to Redux
            if (count > 1 && appointmentIds && appointmentIds.length > 0) {
              try {
                let allCreatedAppointments = [];

                // Method 1: Try using refID to fetch all appointments in the series (more efficient)
                if (refID) {
                  console.log(`Fetching ${count} appointments using refID: ${refID}`);
                  try {
                    const refQuery = query(
                      collection(db, COLLECTIONS.APPOINTMENTS),
                      where("refID", "==", refID)
                    );
                    const refSnapshot = await getDocs(refQuery);
                    allCreatedAppointments = refSnapshot.docs.map(doc => ({
                      id: doc.id,
                      ...doc.data()
                    }));
                    console.log(`Successfully fetched ${allCreatedAppointments.length} appointments using refID`);
                  } catch (refError) {
                    console.warn("Failed to fetch using refID, falling back to batch method:", refError);
                    allCreatedAppointments = []; // Reset for fallback
                  }
                }

                // Method 2: Fallback to batched "IN" queries if refID method failed or returned no results
                if (allCreatedAppointments.length === 0) {
                  console.log(`Fetching ${appointmentIds.length} appointments using batched IN queries`);
                  const batchSize = 30;

                  // Process appointmentIds in batches of 30
                  for (let i = 0; i < appointmentIds.length; i += batchSize) {
                    const batch = appointmentIds.slice(i, i + batchSize);
                    console.log(`Fetching batch ${Math.floor(i / batchSize) + 1}: ${batch.length} appointments`);

                    const batchQuery = query(
                      collection(db, COLLECTIONS.APPOINTMENTS),
                      where("__name__", "in", batch)
                    );

                    const batchSnapshot = await getDocs(batchQuery);
                    const batchAppointments = batchSnapshot.docs.map(doc => ({
                      id: doc.id,
                      ...doc.data()
                    }));

                    allCreatedAppointments.push(...batchAppointments);
                    console.log(`Fetched ${batchAppointments.length} appointments in this batch`);
                  }
                }

                // Add all created appointments to Redux store in a single action
                // This ensures the calendar re-renders properly with all appointments
                console.log(`Successfully fetched ${allCreatedAppointments.length} total appointments from Firebase, adding to Redux`);

                if (allCreatedAppointments.length > 0) {
                  dispatch(addMultipleAppointmentsAction(allCreatedAppointments));
                } else {
                  console.warn("No appointments were fetched from Firebase, using fallback");
                  // Fallback: add just the base appointment data
                  const appointmentWithSeriesId = {
                    ...appointmentData,
                    refID,
                    isRecurringInstance: count > 1
                  };
                  dispatch(addNewAppointmentAction(appointmentWithSeriesId));
                }
              } catch (fetchError) {
                console.error("Error fetching created appointments:", fetchError);
                enqueueSnackbar(`Warning: Created ${count} appointments but couldn't fetch them for display. Please refresh the page.`, { variant: "warning" });

                // Fallback: add just the base appointment data
                const appointmentWithSeriesId = {
                  ...appointmentData,
                  refID,
                  isRecurringInstance: count > 1
                };
                dispatch(addNewAppointmentAction(appointmentWithSeriesId));
              }
            } else {
              // Single appointment - fetch the created appointment from Firebase and add to Redux
              try {
                if (appointmentIds && appointmentIds.length > 0) {
                  const appointmentId = appointmentIds[0];
                  const appointmentDoc = await getDoc(doc(db, COLLECTIONS.APPOINTMENTS, appointmentId));

                  if (appointmentDoc.exists()) {
                    const createdAppointment = { id: appointmentDoc.id, ...appointmentDoc.data() };
                    console.log(`Successfully fetched single appointment from Firebase:`, createdAppointment);
                    dispatch(addNewAppointmentAction(createdAppointment));
                  } else {
                    console.warn("Single appointment document not found, using fallback");
                    // Fallback: add the base appointment data with the returned ID
                    const appointmentWithId = {
                      id: appointmentId,
                      ...appointmentData,
                      refID,
                      isRecurringInstance: count > 1
                    };
                    dispatch(addNewAppointmentAction(appointmentWithId));
                  }
                } else {
                  console.warn("No appointment ID returned for single appointment, using fallback");
                  // Fallback: add just the base appointment data
                  const appointmentWithSeriesId = {
                    ...appointmentData,
                    refID,
                    isRecurringInstance: count > 1
                  };
                  dispatch(addNewAppointmentAction(appointmentWithSeriesId));
                }
              } catch (fetchError) {
                console.error("Error fetching single appointment:", fetchError);
                enqueueSnackbar(`Warning: Created appointment but couldn't fetch it for display. Please refresh the page.`, { variant: "warning" });

                // Fallback: add the base appointment data with ID if available
                if (appointmentIds && appointmentIds.length > 0) {
                  const appointmentWithId = {
                    id: appointmentIds[0],
                    ...appointmentData,
                    refID,
                    isRecurringInstance: count > 1
                  };
                  dispatch(addNewAppointmentAction(appointmentWithId));
                } else {
                  const appointmentWithSeriesId = {
                    ...appointmentData,
                    refID,
                    isRecurringInstance: count > 1
                  };
                  dispatch(addNewAppointmentAction(appointmentWithSeriesId));
                }
              }
            }

            let message;
            if (count > 1) {
              message = `Successfully created  appointments `;
            } else {
              message = "New appointment added successfully";
            }

            enqueueSnackbar(message, { variant: "success" });

            if (watch("submission_type") === "add_another") {
              // Reset form for adding another appointment with a small delay to ensure state updates
              setTimeout(() => {
                reset({
                  overrideShift: false,
                  scheduleWithoutTime: false,
                  startDate: date ? moment(date).format("YYYY-MM-DD") : "",
                  endDate: date ? moment(date).format("YYYY-MM-DD") : "",
                  startTime: "",
                  endTime: "",
                  recurrence: "",
                  serviceType: "",
                  caregiver: "",
                  comments: "",
                  submission_type: "", // Clear the submission type
                });
                // Clear any form errors
                Object.keys(errors).forEach(field => {
                  setError(field, { message: undefined });
                });
              }, 100);
            } else {
              onCloseModal();
            }
          } else {
            throw new Error("Failed to create appointments");
          }
        } catch (error) {
          console.error("Error creating appointments:", error);
          enqueueSnackbar(`Couldn't create the appointment: ${error.message}`, { variant: "error" });
          throw error; // Re-throw to be caught by outer try-catch
        }
      } else {
        enqueueSnackbar("Please select a caregiver", { variant: "warning" });
      }
    } catch (error) {
      console.error("Error in submitForm:", error);
      // Error handling is already done in individual catch blocks
    } finally {
      // Always reset submission state
      setIsFormSubmitting(false);
      submissionInProgress.current = false;
    }
  }

  useEffect(() => {
    if (date) {
      setValue("startDate", moment(date).format("YYYY-MM-DD"));
      setValue("endDate", moment(date).format("YYYY-MM-DD"));
    }
  }, [date]);

  useEffect(() => {
    if (watch("overrideShift") === false && watch("startTime")) {
      const end_time = moment(watch("startTime"), "HH:mm").add(watch("shiftLength"), "hours").format("HH:mm");
      setValue("endTime", end_time);
    }
  }, [watch("overrideShift")]);

  useEffect(() => {
    if (watch("scheduleWithoutTime")) {
      setValue("starTime", "");
      setValue("endTime", "");
    }
  }, [watch("scheduleWithoutTime")]);

  useEffect(() => {
    if (open && isFromCalendar) {
      setValue("isFromCalendar", isFromCalendar);
      setValue("caregiver", defaultValues?.caregiver);
    }
  }, [open]);

  

  return (
    <ModalWindow isVisible={open} visibilityHandler={onCloseModal}>
      <Container className={open ? "visible" : ""} top={-100}>
        {isFromCalendar && (
          <>
            <div className="header">
              <div className="user">{name}</div>
            </div>
            <Divider sx={{ mt: 2 }} />
          </>
        )}
        <form onSubmit={handleSubmit(submitForm)}>
          <InnerContainer>
            {/* SELECT SHIFT LENGTH */}
            <Box sx={{ gridColumn: "1/3" }}>
              <LabeledFormInput
                id={`shift-length`}
                title="Shift Length"
                placeholder=""
                customInput={
                  <CustomSelect
                    label={`Shift Length`}
                    placeholder=""
                    options={SHIFT_LENGTH}
                    value={SHIFT_LENGTH.find((item) => item.value === watch("shiftLength"))}
                    variant="basic"
                    changeHandler={(opt) => onSelectShiftLength(opt?.value)}
                    disabled={watch("overrideShift")}
                    key={watch("shiftLength")}
                  />
                }
              />
              {errors?.shiftLength?.message && (
                <Typography color="error" variant="caption">
                  {errors?.shiftLength?.message}
                </Typography>
              )}
            </Box>
            <Divider sx={{ m: 0, gridColumn: "1/3" }} />

            {/* OVERRIDE SHIFT LENGTH */}
            <FormControlLabel
              sx={{ fontSize: 16, gridColumn: "1/3" }}
              label={<>Override Shift Length</>}
              control={
                <Checkbox
                  color="primary"
                  checkedIcon={<CheckboxIcon />}
                  icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
                  value={watch("overrideShift")}
                  checked={watch("overrideShift")}
                  onChange={(e) => {
                    setValue("overrideShift", e?.target?.checked);
                  }}
                  key={watch("overrideShift")}
                />
              }
            />

            {/* SCHEDULE WITHOUT TIME */}
            {/* <FormControlLabel
              sx={{ fontSize: 16 }}
              label={<>Schedule without Time</>}
              control={
                <Checkbox
                  color="primary"
                  checkedIcon={<CheckboxIcon />}
                  icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
                  onChange={(e) => {
                    setValue("scheduleWithoutTime", e?.target?.checked);
                  }}
                  key={watch("scheduleWithoutTime")}
                />
              }
            /> */}
            <Divider sx={{ m: 0, gridColumn: "1/3" }} />

            {/* FROM DATE & TIME */}
            <Box
              sx={{
                gridColumn: "1/3",
                display: "flex",
                justifyContent: "space-between",
                alignItems: { xs: "flex-start", sm: "center" },
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 0.5, md: 0 },
              }}
            >
              <FormLabel sx={{ display: { xs: "block", md: "inline" } }}>From</FormLabel>
              <Box display="flex" columnGap={1} flexDirection={{ xs: "column important", sm: "row" }}>
                <DateInput
                  id="date"
                  minDate={new Date()}
                  maxDate={moment().add(2, "months").endOf("month").toDate()}
                  onChange={(val) => {
                    const formatted_val = moment(val).format("YYYY-MM-DD");
                    setValue("startDate", formatted_val);
                    setValue("endDate", formatted_val);
                  }}
                  value={watch("startDate")}
                  // key={watch("startDate")}
                />
                <CustomSelect
                  label="Start Time"
                  placeholder="Select start time"
                  options={TIME_OPTIONS}
                  value={TIME_OPTIONS.find((item) => item.value === watch("startTime"))}
                  variant="basic"
                  changeHandler={onSelectStartTime}
                  disabled={watch("scheduleWithoutTime")}
                  isSearchable={false}
                  key={watch("startTime")}
                  
                />
              </Box>
            </Box>
            {errors?.startTime?.message && (
              <Typography color="error" variant="caption">
                {errors?.startTime?.message}
              </Typography>
            )}

            {/* END DATE & TIME */}
            <Box
              sx={{
                gridColumn: "1/3",
                display: "flex",
                justifyContent: "space-between",
                alignItems: { xs: "flex-start", sm: "center" },
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 0.5, md: 0 },
              }}
            >
              <FormLabel>To</FormLabel>
              <Box display="flex" columnGap={1} flexDirection={{ xs: "column important", sm: "row" }}>
                <DateInput
                  id="endDate"
                  minDate={new Date()}
                  maxDate={moment().add(2, "months").endOf("month").toDate()}
                  value={watch("endDate")}
                  disabled
                  // key={watch("endDate")}
                />
                <CustomSelect
                  label="End Time"
                  placeholder="Select end time"
                  options={TIME_OPTIONS}
                  value={TIME_OPTIONS.find((item) => item.value === watch("endTime"))}
                  variant="basic"
                  changeHandler={onSelectEndTime}
                  disabled={watch("scheduleWithoutTime") || !watch("overrideShift")}
                  isSearchable={false}
                  key={watch("endTime")}
                />
              </Box>
            </Box>
            {errors?.endTime?.message && (
              <Typography color="error" variant="caption">
                {errors?.endTime?.message}
              </Typography>
            )}

            {/* RECURRENCE */}
            <Box sx={{ gridColumn: "1/3" }}>
              <LabeledFormInput
                id={`Recurrence`}
                title="Recurrence"
                placeholder=""
                customInput={
                  <CustomSelect
                    label={`Recurrence`}
                    placeholder=""
                    options={RECURRENCE}
                    variant="basic"
                    value={RECURRENCE.find((item) => item.value === watch("recurrence"))}
                    changeHandler={(opt) => {
                      setValue("recurrence", opt?.value);
                      setError("recurrence", { message: undefined });
                    }}
                    key={watch("recurrence")}
                  />
                }
              />
              {errors?.recurrence?.message && (
                <Typography color="error" variant="caption">
                  {errors?.recurrence?.message}
                </Typography>
              )}
            </Box>

            {/* SERVICE TYPE */}
            <Box sx={{ gridColumn: "1/3" }}>
              <LabeledFormInput
                id={`ServiceType`}
                title="ServiceType"
                placeholder="Service Type"
                customInput={
                  <CustomSelect
                    label={`Service Type`}
                    placeholder="Service Type"
                    options={SERVICE_OPTIONS}
                    value={SERVICE_OPTIONS.find((item) => item.value === watch("serviceType"))}
                    variant="basic"
                    changeHandler={(opt) => {
                      setValue("serviceType", opt?.value);
                      setError("serviceType", { message: undefined });
                      setValue("caregiver", "");
                    }}
                    key={watch("serviceType")}
                    isSearchable={false}
                  />
                }
              />
              {errors?.serviceType?.message && (
                <Typography color="error" variant="caption">
                  {errors?.serviceType?.message}
                </Typography>
              )}
            </Box>

            {/* CAREGIVER */}
            <Box sx={{ gridColumn: "1/3" }}>
              <LabeledFormInput
                id={`Caregiver`}
                title="Caregiver"
                placeholder="Caregiver"
                customInput={
                  <CustomSelect
                    label={`Caregiver`}
                    placeholder="Caregiver"
                    options={caregivers_options}
                    value={caregivers_options?.find((item) => item?.value === watch("caregiver"))}
                    variant="basic"
                    changeHandler={(opt) => {
                      setValue("caregiver", opt?.value);
                      setError("caregiver", { message: undefined });
                    }}
                    key={watch("caregiver")}
                    isSearchable={false}
                  />
                }
              />
              {errors?.caregiver?.message && (
                <Typography color="error" variant="caption">
                  {errors?.caregiver?.message}
                </Typography>
              )}
            </Box>

            {/* COMMENTS */}
            <Box position="relative" height="fit-content" mt={2} sx={{ gridColumn: "1/3" }}>
              <TextArea
                placeholder="Comments"
                id="comments"
                value={watch("comments")}
                handler={(e) => {
                  setValue("comments", e?.target?.value);
                }}
              />
            </Box>

            {/* Buttons */}
            <Box sx={{ gridColumn: "1/3", display: "flex", columnGap: 1 }}>
              <Btn
                type="submit"
                text={isFormSubmitting ? "Saving..." : "Save & Close"}
                className={(isSubmitting || isFormSubmitting) ? "disabled" : ""}
                disabled={isSubmitting || isFormSubmitting}
              />

              <Btn
                type="button"
                text={isFormSubmitting ? "Saving..." : "Save & Add Another"}
                className={(isSubmitting || isFormSubmitting) ? "disabled" : ""}
                disabled={isSubmitting || isFormSubmitting}
                handler={() => {
                  if (!isFormSubmitting && !isSubmitting) {
                    setValue("submission_type", "add_another");
                    handleSubmit(submitForm)();
                  }
                }}
              />

              <Btn
                type="button"
                text="Cancel"
                handler={onCloseModal}
                className={(isSubmitting || isFormSubmitting) ? "disabled" : ""}
                disabled={isSubmitting || isFormSubmitting}
              />
            </Box>
          </InnerContainer>
        </form>
      </Container>
    </ModalWindow>
  );
};

export default AssignShiftModal;
