// styled components
import { DateInputWrapper } from "./style";
import { Input } from "@ui/Field";

// components
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";

// utils
import { useRef, useState } from "react";
import moment from "moment";

const DateInput = ({ id, maxDate, onChange, placeholder, value, minDate, disabled }) => {
  const [open, setOpen] = useState(false);
  const customInputRef = useRef(null);
  const buttonRef = useRef(null);

  // Handle mobile touch events properly
  const handleOpen = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setOpen(true);
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterMoment}>
      <DatePicker
        open={open}
        value={value}
        maxDate={maxDate}
        minDate={minDate}
        onChange={(newValue) => {
          onChange(newValue);
        }}
        disableHighlightToday
        disabled={disabled}
        onClose={handleClose}
        PopperProps={{
          anchorEl: customInputRef.current,
          sx: {
            zIndex: 30000,
            '& .MuiPaper-root': {
              zIndex: 30000
            }
          },
          // Ensure proper positioning on mobile
          placement: 'bottom-start',
          modifiers: [
            {
              name: 'preventOverflow',
              enabled: true,
              options: {
                altAxis: true,
                altBoundary: true,
                tether: false,
                rootBoundary: 'viewport',
              },
            },
            {
              name: 'flip',
              enabled: true,
              options: {
                altBoundary: true,
                rootBoundary: 'viewport',
                padding: 8,
              },
            },
          ],
        }}
        PaperProps={{
          className: "date-picker",
          sx: {
            zIndex: 30000,
            position: 'relative'
          }
        }}
        renderInput={({ ref, inputProps, disabled, onChange, value }) => (
          <DateInputWrapper ref={ref}>
            <Input
              id={id}
              value={value && moment(value).format("MMM DD, YYYY")}
              onChange={onChange}
              disabled={disabled}
              ref={customInputRef}
              placeholder={placeholder}
              {...inputProps}
              onClick={handleOpen}
              onTouchStart={handleOpen}
              style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}
            />
            <i
              className="icon icon-calendar"
              ref={buttonRef}
              onClick={handleOpen}
              onTouchStart={handleOpen}
              style={{
                cursor: disabled ? 'not-allowed' : 'pointer',
                pointerEvents: disabled ? 'none' : 'auto'
              }}
            />
          </DateInputWrapper>
        )}
      />
    </LocalizationProvider>
  );
};

export default DateInput;
