import styled from 'styled-components/macro';
import {colors} from '@styles/vars';
import theme from 'styled-theming';

export const DateInputWrapper = styled.div`
  position: relative;
  cursor: pointer;
  // Ensure proper touch handling on mobile
  touch-action: manipulation;

  input {
    width: 100%;
    // Improve mobile touch target
    min-height: 40px;
    // Prevent zoom on iOS
    font-size: 16px;

    &::placeholder {
      text-transform: uppercase;
    }

    // Improve mobile interaction
    &:focus {
      outline: none;
    }
  }

  .icon {
    position: absolute;
    top: 50%;
    right: 16px;
    transform: translateY(-50%);
    font-size: 14px;
    color: ${theme('theme', {
      light: '#A2C0D4',
      dark: colors.gray,
    })};
    transition: color var(--transition);
    // Improve mobile touch target
    min-width: 24px;
    min-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    // Ensure proper touch handling
    touch-action: manipulation;

    &:hover, &:focus {
      color: ${colors.blue};
    }

    // Better mobile touch feedback
    @media (hover: none) and (pointer: coarse) {
      &:active {
        color: ${colors.blue};
        transform: translateY(-50%) scale(1.1);
      }
    }
  }
`;