import React from "react";
import { StyledForm } from "@widgets/UserSettings/style";
import CustomSelect from "@ui/Select";
import { Button, Checkbox, CircularProgress, Divider, FormControlLabel, Grid } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Typography } from "@mui/material";
import styled from "styled-components";
import { textSizes } from "@styles/vars";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "notistack";
import { doc, updateDoc } from "firebase/firestore";
import { db } from "@config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import { updateUserAction } from "@store/slices/users";
import Field from "@ui/Field";
import TextArea from "@ui/TextArea/TextArea";
import { CheckBox, CheckBoxOutlineBlank } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";
import AssignShiftModal from "@components/AppointmentsCalendar/AssignShiftModal";
import { useState } from "react";
import moment from "moment";

const Label = styled.label`
  font-size: ${textSizes["14"]};
  width: fit-content;
  margin-bottom: 8px;
  display: block;
`;

const StyledField = styled(Field)`
  width: 100%;
`;

const DocPreview = styled.div`
  position: relative;
  margin-top: 8px;
  border-radius: 8px;
  border: 1px solid lightgrey;
  img {
    height: 60px;
    width: 100px;
    display: block;
    border-radius: 8px;
  }
  button {
    position: absolute;
    display: inline-block;
    top: 0;
    right: 0;
    z-index: 10;
    background-color: rgba(75, 75, 75, 0.39);
    height: 24px;
    width: 24px;
    border-radius: 99px;
  }
`;

const YES_NO_OPTIONS = [
  { label: "Yes", value: true },
  { label: "No", value: false },
];
const RECOMMENDED_SERVICES = [
  "Daily Caregiver Support",
  "Weekly Nursing Visit",
  "Doctor Home Visit",
  "Medication Management",
  "Physical Therapy",
  "Nutrition Counseling",
  "Post-surgical Recovery",
  "Palliative Care",
];

const step4Schema = z
  .object({
   bloodPressure: z.string()
    .nonempty("Required")
    .min(5, "Blood pressure format should be systolic/diastolic (e.g. 120/80)")
    .max(15, "Blood pressure value is too long")
    .refine(
      (val) => {
        // Enforce format: 3 digits / 2–3 digits, optional " mmHg"
        const regex = /^\d{3}\/\d{2,3}(?:\s*mmHg)?$/;
        return regex.test(val);
      },
      {
        message: "Invalid format. Use format like '120/80' or '120/80 mmHg'",
      }
    ),
    temperature: z.string()
  .nonempty("Required")
  .min(3, "Temperature value is too short")
  .max(6, "Temperature value is too long")
  .refine(
    (val) => {
      // Accepts formats like  "98.6 F" 
      const regex = /^\d{2,3}(\.\d{1,2})?\s*(F)?$/i;
      return regex.test(val);
    },
    {
      message: "Invalid temperature format. Use format '98.6 F' "
    }
  )
  .refine(
    (val) => {
      // Extract the numeric value and unit
      const match = val.match(/^(\d{2,3}(?:\.\d{1,2})?)\s*(F|C)?$/i);
      if (!match) return false;
      
      const temp = parseFloat(match[1]);
      const unit = (match[2] || "F").toUpperCase();
      
      // Check if temperature is within reasonable range
      if (unit === "F") {
        return temp >= 90 && temp <= 110; // Reasonable Fahrenheit range for humans
      } else {
        return temp >= 32 && temp <= 43; // Reasonable Celsius range for humans
      }
    },
    {
      message: "Temperature value is outside reasonable range"
    }
  ),
    pulse: z.preprocess(
  (value) => {
    const parsed = parseInt(value);
    return isNaN(parsed) ? value : parsed;
  },
  z.number({ message: "Pulse must be a number" })
    .int({ message: "Pulse must be a whole number" })
    .positive({ message: "Only positive numbers allowed" })
    .min(30, "Pulse is too low (minimum 30 bpm)")
    .max(220, "Pulse is too high (maximum 220 bpm)")
),
    spo2: z.preprocess(
  (value) => {
    const parsed = parseInt(value);
    return isNaN(parsed) ? value : parsed;
  },
  z.number({ message: "SpO2 must be a number" })
    .int({ message: "SpO2 must be a whole number" })
    .positive({ message: "Only positive numbers allowed" })
    .min(70, "SpO2 is too low (minimum 70%)")
    .max(100, "SpO2 cannot exceed 100%")
),
    bloodSugar: z.string()
  .nonempty("Required")
  .refine(
    (val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0,
    { message: "Blood sugar must be a valid positive number" }
  )
  .refine(
    (val) => {
      const num = parseFloat(val);
      return num >= 20 && num <= 600;
    },
    { message: "Blood sugar should be between 20-600 mg/dL" }
  ),
    wounds: z.string().optional().default(""),
    medAdminRequired: z.boolean({ message: "Required" }).default(null),
    needLabs: z.boolean({ message: "Required" }).default(null),
    recommendedServices: z.array(z.string()).min(1, "Select atleast one service"),
    otherServiceText: z.string().optional().default(""),
  })
  .superRefine((data, ctx) => {
    if (data?.recommendedServices.find((item) => item.startsWith("Other:")) && !data?.otherServiceText?.trim()) {
      ctx.addIssue({
        path: ["otherServiceText"],
        code: z.ZodIssueCode.custom,
        message: "Provide the other service",
      });
    }
  });

const MAX_DATE_DOB = new Date();
MAX_DATE_DOB.setFullYear(new Date().getFullYear() - 5);

const AddPatientStep4 = ({ gotoNext, goBack, canGoBack, setCurrentPatient, currentPatient }) => {
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isScheduleOpen, setScheduleOpen] = useState(false);

  const { user: logged_in_user } = useSelector((state) => state.auth);
  const { nurses, caregivers } = useSelector((state) => state.users);
  const commonNurse = () => {
    if (logged_in_user?.role === "ADMIN") {
      return nurses?.find((item) => item?.id === currentPatient?.assignedNurse);
    } else {
      return logged_in_user;
    }
  };
  const caregiver_options = caregivers
    ?.filter((item) => item?.nurse === commonNurse()?.id)
    ?.map((item) => ({ label: item?.name, value: item?.id }));

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    setValue,
    setError,
    watch,
  } = useForm({
    defaultValues: {
      bloodPressure: "",
      temperature: "",
      spo2: "",
      pulse: "",
      bloodSugar: "",
      wounds: "",
      medAdminRequired: null,
      needLabs: null,
      recommendedServices: [],
      otherServiceText: "",
    },
    resolver: zodResolver(step4Schema),
    mode: "all",
  });

  // console.log("currentPatient ", currentPatient);
  // console.log("common.nurse ", commonNurse());
  // console.log("caregivers ", caregiver_options);

  function onToggleService(val) {
    const recommendedServices = watch("recommendedServices");
    if (recommendedServices?.includes(val)) {
      setValue(
        "recommendedServices",
        recommendedServices.filter((diagnosis) => diagnosis !== val),
      );
      val === "Other:" && setValue("otherServiceText", "");
    } else {
      setValue("recommendedServices", [...recommendedServices, val]);
    }
    if (watch("recommendedServices").length > 0) {
      setError("recommendedServices", { message: undefined });
    } else {
      setError("recommendedServices", { message: "Select atleast one service" });
    }
  }

  // * SUBMIT FORM
  async function submitForm(formValues) {
    if (currentPatient?.id) {
      const { otherServiceText, recommendedServices, ...rest } = formValues;

      const formatted_services = recommendedServices?.map((diagnosis) =>
        diagnosis === "Other:" ? `Other: ${otherServiceText}` : diagnosis,
      );

      console.log("formatted_services ", formatted_services);

      const payload = {
        ...rest,
        recommendedServices: formatted_services,
        currentOnboardStep: 5,
        onboardPercentage: 100,
      };

      await updateDoc(doc(db, COLLECTIONS.USERS, currentPatient?.id), payload)
        .then(() => {
          const updated_patient = {
            ...currentPatient,
            ...payload,
          };
          dispatch(updateUserAction(updated_patient));
          enqueueSnackbar("Pateint's data updated", { variant: "success" });
          setCurrentPatient(updated_patient);
          setScheduleOpen(true);
          // navigate("/clients");
        })
        .catch(() => enqueueSnackbar("Couldn't update the patient's data", { variant: "error" }));
    }
  }

  useEffect(() => {
    if (currentPatient?.id) {
      currentPatient?.bloodPressure && setValue("bloodPressure", currentPatient?.bloodPressure);
      currentPatient?.temperature && setValue("temperature", currentPatient?.temperature);
      currentPatient?.spo2 && setValue("spo2", currentPatient?.spo2);
      currentPatient?.pulse && setValue("pulse", currentPatient?.pulse);
      currentPatient?.bloodSugar && setValue("bloodSugar", currentPatient?.bloodSugar);
      currentPatient?.wounds && setValue("wounds", currentPatient?.wounds);
      setValue("medAdminRequired", Boolean(currentPatient?.medAdminRequired));
      setValue("needLabs", Boolean(currentPatient?.needLabs));
      currentPatient?.recommendedServices?.length &&
        setValue(
          "recommendedServices",
          currentPatient?.recommendedServices?.map((item) => (item?.startsWith("Other:") ? "Other:" : item)),
        );

      if (currentPatient?.recommendedServices?.find((item) => item?.includes("Other:"))) {
        setValue(
          "otherServiceText",
          currentPatient?.recommendedServices?.find((item) => item?.includes("Other:"))?.split("Other: ")[1],
        );
      }
    }
  }, []);

  return (
    <>
      <StyledForm onSubmit={handleSubmit(submitForm)}>
        <Grid container spacing={2}>
          <Grid size={12}>
            <Typography variant="h6" fontWeight={500} mt={2}>
              Nursing Assessment
            </Typography>
          </Grid>

          <Grid size={12} spacing={2}>
            <Typography fontWeight={500}>Vital Signs</Typography>
          </Grid>

          {/* BLOOD PRESSURE */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <Controller
              name="bloodPressure"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="bp">Blood Pressure (mmHg)</Label>
                    <StyledField type="text" id="bp" {...field} placeholder="Ex: 120mmHg - 80 mmHg" />
                  </>
                );
              }}
            />
            {errors?.bloodPressure?.message && (
              <Typography color="error" variant="caption">
                {errors?.bloodPressure?.message}
              </Typography>
            )}
          </Grid>

          {/* TEMPERATURE */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <Controller
              name="temperature"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="temp">Temperature (F)</Label>
                    <StyledField type="text" id="temp" {...field} placeholder="Ex: 98.7 F" />
                  </>
                );
              }}
            />
            {errors?.temperature?.message && (
              <Typography color="error" variant="caption">
                {errors?.temperature?.message}
              </Typography>
            )}
          </Grid>

          {/* PULSE */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <Controller
              name="pulse"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="pulse">Pulse (bpm)</Label>
                    <StyledField type="text" id="pulse" {...field} placeholder="Ex: 82 bpm" />
                  </>
                );
              }}
            />
            {errors?.pulse?.message && (
              <Typography color="error" variant="caption">
                {errors?.pulse?.message}
              </Typography>
            )}
          </Grid>

          {/* SPO2 */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <Controller
              name="spo2"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="spo2">SpO2</Label>
                    <StyledField type="text" id="spo2" {...field} placeholder="Ex: 95%" />
                  </>
                );
              }}
            />
            {errors?.spo2?.message && (
              <Typography color="error" variant="caption">
                {errors?.spo2?.message}
              </Typography>
            )}
          </Grid>

          {/* BLOOD SUGAR */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <Controller
              name="bloodSugar"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="bloodSugar">Blood Sugar (mg/dL)</Label>
                    <StyledField type="text" id="bloodSugar" {...field} placeholder="Ex: 100 mg/dL" />
                  </>
                );
              }}
            />
            {errors?.bloodSugar?.message && (
              <Typography color="error" variant="caption">
                {errors?.bloodSugar?.message}
              </Typography>
            )}
          </Grid>

          <Grid size={{ xs: 12, sm: 6 }}></Grid>

          {/* MEDICATION ADMINISTRATION REQUIRED */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <Controller
              name="medAdminRequired"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="medAdminRequired">Injections or medication administration required</Label>
                    <CustomSelect
                      placeholder="Select"
                      options={YES_NO_OPTIONS}
                      changeHandler={(option) => field.onChange(option.value)}
                      value={YES_NO_OPTIONS.find((item) => item.value === field.value)}
                    />
                  </>
                );
              }}
            />
            {errors?.medAdminRequired?.message && (
              <Typography color="error" variant="caption">
                {errors?.medAdminRequired?.message}
              </Typography>
            )}
          </Grid>

          {/* NEED LABS */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <Controller
              name="needLabs"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="needLabs">Labs needed</Label>
                    <CustomSelect
                      placeholder="Select"
                      options={YES_NO_OPTIONS}
                      changeHandler={(option) => field.onChange(option.value)}
                      value={YES_NO_OPTIONS.find((item) => item.value === field.value)}
                    />
                  </>
                );
              }}
            />
            {errors?.needLabs?.message && (
              <Typography color="error" variant="caption">
                {errors?.needLabs?.message}
              </Typography>
            )}
          </Grid>

          {/* WOUNDS */}
          <Grid size={12}>
            <Controller
              name="wounds"
              control={control}
              render={({ field }) => {
                return (
                  <>
                    <Label htmlFor="wounds">Wound or skin issues</Label>
                    <TextArea {...field} placeholder="Describe wounds or skin issues" id="wounds" rows={3} />
                  </>
                );
              }}
            />
            {errors?.wounds?.message && (
              <Typography color="error" variant="caption">
                {errors?.wounds?.message}
              </Typography>
            )}
          </Grid>
        </Grid>

        <Divider sx={{ my: 2, mt: 4 }} />

        <Grid container spacing={2}>
          <Grid size={12}>
            <Typography variant="h6" fontWeight={500} mt={1}>
              Recommended Services
            </Typography>
          </Grid>
          {errors?.recommendedServices?.message && (
            <Grid size={12}>
              <Typography color="error" variant="caption">
                {errors?.recommendedServices?.message}
              </Typography>
            </Grid>
          )}

          {/* ORIENTATION */}
          {RECOMMENDED_SERVICES.map((item, index) => (
            <Grid size={{ xs: 12, sm: 6 }} key={index}>
              <FormControlLabel
                sx={{ mb: 0 }}
                label={item}
                control={
                  <Checkbox
                    color="primary"
                    checkedIcon={<CheckBox />}
                    icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
                    checked={watch("recommendedServices")?.includes(item)}
                    onChange={() => onToggleService(item)}
                  />
                }
              />
            </Grid>
          ))}

          <Grid size={{ xs: 12, sm: 6 }}>
            <FormControlLabel
              label="Other"
              control={
                <Checkbox
                  color="primary"
                  checkedIcon={<CheckBox />}
                  icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
                  checked={watch("recommendedServices")?.find((item) => item.startsWith("Other:"))}
                  onChange={() => onToggleService("Other:")}
                  key={watch("recommendedServices")?.length}
                />
              }
            />

            {watch("recommendedServices")?.find((item) => item.startsWith("Other:")) ? (
              <Controller
                name="otherServiceText"
                control={control}
                render={({ field }) => {
                  return (
                    <>
                      <StyledField type="text" id="otherServiceText" {...field} placeholder="Other Service" />
                      {errors?.otherServiceText?.message && (
                        <Typography color="error" variant="caption">
                          {errors?.otherServiceText?.message}
                        </Typography>
                      )}
                    </>
                  );
                }}
              />
            ) : null}
          </Grid>

          <Grid
            size={12}
            sx={{
              display: "flex",
              marginTop: 8,
              justifyContent: "space-between",
              alignItems: "center",
              gap: 1.5,
            }}
          >
            <Button
              variant="outlined"
              color="primary"
              type="button"
              sx={{
                fontSize: 16,
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 400,
                maxWidth: 200,
                width: "100%",
              }}
              disabled={!canGoBack || isSubmitting}
              onClick={goBack}
            >
              {"Back"}
            </Button>
            <Button
              variant="contained"
              color="primary"
              type="submit"
              sx={{
                fontSize: 16,
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 400,
                maxWidth: 200,
                width: "100%",
              }}
              disabled={isSubmitting}
            >
              {"Finish"}
              {isSubmitting ? <CircularProgress size={16} color="inherit" sx={{ marginLeft: 1 }} /> : null}
            </Button>
          </Grid>
        </Grid>
      </StyledForm>

      {/* ASSIGN SHIFT MODAL */}
      <AssignShiftModal
        elemsHeight={0}
        mode="add_client"
        name={null}
        open={isScheduleOpen}
        handler={(val) => {
          setScheduleOpen(val);
          if (val === false) {
            navigate("/clients");
          }
        }}
        date={moment().add(1, "days").format("YYYY-MM-DD")}
        client_options={[]}
        defaultValues={{ client: currentPatient?.id }}
        commonNurse={commonNurse()}
        caregiver_options={caregiver_options}
      />
    </>
  );
};

export default AddPatientStep4;
